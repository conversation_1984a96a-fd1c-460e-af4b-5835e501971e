/** @file   mmsPacketUtil.h
 * @brief   mmsPacket 처리 유틸 헤드 파일
 * @date    2008-01-23
 * <AUTHOR>
 */

#ifndef _KSKYB_MMS_PACKET_UTIL_H_
#define _KSKYB_MMS_PACKET_UTIL_H_

#include <stdio.h>
#include <string>
#include <list>
#include <vector>
#include "mmsPacketBase.h"
#include "Encrypt.h"
#include "ksbase64.h"


using namespace std;

class CContentType {
    public:
        string strType;
        string strBoundary;
        string strCharset;
        string strName;
};

class CMData {
    public:
        CContentType contentType;
        string strEncoding;
        string strSvc;
        string strData;
        int cntid;
        int ctnseq;
};

class CBcastData {
    public:
        string strSeq;
        string strKey;
        string strExtend;
        string strReceiver;
};


// CMMSPacketSend
// CMMSPacketBase
// CMMSPacketAck
// CMMSPacketRpt
// CMMSPacketRak
class CMMSPacketSend : public CMMSPacketBase {
    private:
        typedef list<CBcastData>::iterator listCBcastDataPosition;
        typedef list<string>::iterator listBcastDataPosition;
        typedef list<CMData>::iterator listMdataPosition;

        listMdataPosition m_MDataPos;
        listCBcastDataPosition m_BcastData;

        string strId;
        string strKey;
		// default : utf8, euckr
		string strEncoding;
		string strChatType;
		string strTargeting;
        string strSubject;
        string strReceiver;
        string strSender;
        string strContentCnt;
        string strTextCnt;
        string strImgCnt;
        string strSenderKey;
        string strTmplCd;
		string strPushAlarm;
		string strAppUserId;
		string strTestFriend;
        string strButton;
        string strMsgOrgSize;
        string strPhoneSize;
        string strMsgBody;
		string strImgPath;
		string strImgLink;
		string strUserKey;
		string strMsgGrpCd;
		string strAdFlag;
		string strWide;
		string strKkoImgUrl;
		string strEncrypt;		    //encrypt : aes_base64
		string strMessageVariable;
		string strButtonVariable;
		string strCouponVariable;
		string strImageVariable;
		string strVideoVariable;
		string strCommerceVariable;
		string strCarouselVariable;
		string strRepFlag;			//S:SMS, L:LMS, N:None
	    string strRepMsgTitle;
		string strRepMsgBody;		//Replace Message Body
		string strReserve;
		string strVarType;			// 'vari' : variable, 'tran': Transaction type

        list<CBcastData> listCBcastData;
        list<string> listBcastData;
        string strMDataVersion;
        CContentType contentType;
        list<CMData> listMData;
        string strErrorMsg;
        int ctnseq;
        int nCtnType;
        int nCtnTypeCnt;
        int nTextCnt;
        int nImgCnt;
        int nAugCnt;
        int nMpCnt;
		string strNationCode;
		string strExtend;
		string strResellerCode;
		string strUnsubPhone;
		string strUnsubAuth;
		string strAddContent;
		string strAdult;
		string strHeader;
		//string strMessage;
		string strAttachment;
		//string strCarousel;

	public:
        CMMSPacketSend();
        ~CMMSPacketSend();
        int parse(char* data);
		int parse_free(char *data);
		int getCtnType();
        void display();

	private:
        void parseBcastData(char* szOrg);
        void pushBcastData(string data);


//        void parseMData(char* szOrg);				// 20140218 return int
        int  parseMData(char* szOrg);

        char* setMimeVersion(char* szOrg);
        char* setMDataHeader(char* szOrg);
        char* setMDataBody(char* szOrg,char* szTag);

        void setCtnType();

        char* getContentType(char* szOrg,CContentType &contentType);
        char* getEachMData(char*szOrg, char* szTag, char* szBoundary, string &data);
        char* getContentEncoding(char* szOrg, char* szTag, CMData &mData);
        char* getContentSvc(char* szOrg, char* szTag, CMData &mData);

        char* getEachMDataBodyHeader(char* szOrg, char* szTag,string &strMDataBodyHeader);
        char* getEachMDataBodyData(char* szOrg, char* szTag,string &strMDataBodyData);

        int getCMData(CMData& mData,char* header, char* body);

        // findLine
        char* matchString(char* szOrg, const char *szTag, string &szVal);
        // findField
        char* findFirstField(char* szOrg, char* szTag, string &szVal);
        char* findOtherField(char* szOrg, char* szTag, string &szVal);

    public:
        char* getIdValue();
        char* getKeyValue();
		char* getEncodingValue();
		char* getChatTypeValue();
		char* getTargetingValue();

        char* getSubjectValue();
        char* getReceiverValue();
        char* getSenderValue();
        char* getContentCntValue();
        char* getTextCntValue();
        char* getImgCntValue();
        char* getSenderKeyValue();
        char* getTmplCdValue();
        char* getButtonValue();
		char* getMsgOrgSizeValue();
		char* getPhoneSizeValue();
		char* getMsgBodyValue();
		char* getImgPathValue();
		char* getImgLinkValue();
		char* getUserKeyValue();
		char* getMsgGrpCdValue();
        char* getErrorMsg();
        char* getAdFlagValue();
        char* getWideValue();
		char* getKkoImgUrlValue();

		char* getEncryptValue();
		char* getPushAlarmValue();
		char* getAppUserIdValue();
		char* getTestFriendValue();
		char* getMessageVariableValue();
		char* getButtonVariableValue();
		char* getCouponVariableValue();
		char* getImageVariableValue();
		char* getVideoVariableValue();
		char* getCommerceVariableValue();
		char* getCarouselVariableValue();
		char* getRepFlagValue();
		char* getRepMsgTitleValue();
		char* getRepMsgBodyValue();
		char* getReserveValue();
		char* getNationCodeValue();
		char* getResellerCodeValue();
		char* getUnsubPhoneValue();
		char* getUnsubAuthValue();
		char* getAdultValue();
		char* getHeaderValue();
		char* getAttachmentValue();
		char *getAddContentValue();

		int getTextCnt() { return nTextCnt; }
        int getImgCnt() { return nImgCnt; }

		void setMsgBody(string _msgBody){strMsgBody = _msgBody;}
		void setImgPath(string _imgPath){strImgPath = _imgPath;}


            // Set variable/transaction type marker (e.g., "vari" or "tran")
            void setVarType(const std::string& t) { strVarType = t; }
            const std::string& getVarType() const { return strVarType; }

        int getCtnSeq();

        int getMDataFirst(CMData& mData);
        int getMDataNext(CMData& mData);

        int getBcastFirst(CBcastData& bCastData);
        int getBcastNext(CBcastData& bCastData);

        inline string trim_left(const string& str)
        {
            int n = str.find_first_not_of(" \t\v\n");
            return n == string::npos ? str : str.substr(n, str.length());
        }

        inline string trim_right(const string& str)
        {
            int n = str.find_last_not_of(" \t\v\n");
            return n == string::npos ? str : str.substr(0, n + 1);
        }

        void split(std::string& text, std::string& separators, std::vector<std::string>& words);
		string decAesBase(Encrypt &en, const char * encStr, int encSize);

};

#endif

