/** @file   mmsPacketUtil.cpp
 * @brief   mmsPacket 파싱에 필요한 메소드 모음
 */


#include "mmsPacketSend.h"


CMMSPacketSend::CMMSPacketSend()
{
    ctnseq = 0;
    nCtnType = 0;
}

CMMSPacketSend::~CMMSPacketSend()
{
}


/** @brief MData 헤더에서 Content-Type를 가져온다.
 * @return Content-Type을 가져온 이후 위치 포인트를 반환한다.
 */
char* CMMSPacketSend::getContentType(char* szOrg,CContentType &contentType)
{
    char* szStt=NULL;
    char* szEnd=NULL;
    char* szTag="MDATA";
    char* szFieldStt=NULL;
    string strVal;
    string strField;

    szStt=szOrg;
    contentType.strType = "";
    contentType.strBoundary = "";
    contentType.strCharset = "";
    contentType.strName = "";

    contentType.strType.reserve(0);
    contentType.strBoundary.reserve(0);
    contentType.strCharset.reserve(0);
    contentType.strName.reserve(0);

    while(1)
    {
        szStt = matchString(szStt,szTag,strVal);
        if( szStt == NULL )
        {
            // 현재 끝일경우도 여기로 와서 끝난다. 처리 할수 있음 처리 하기
            strErrorMsg = "Content-Type 값을 찾을수 없습니다.";
            return NULL;
        }

        szFieldStt = findFirstField((char*)strVal.c_str(),"Content-Type",strField);
		// 20140212 comment
//      if( szFieldStt == NULL )
//          continue;

        contentType.strType = trim_left(trim_right(strField));

        if( strstr(contentType.strType.c_str(),"TXT") )
        {
            nCtnTypeCnt += 1;
            nTextCnt++;
        }
		else if (strstr(contentType.strType.c_str(),"IMG"))
		{
            nCtnTypeCnt += 10;
            nImgCnt++;
        }
		else if (strstr(contentType.strType.c_str(),"SND"))
		{
            nCtnTypeCnt += 100;
            nAugCnt++;
        }

        szFieldStt = findOtherField((char*)strVal.c_str(),"boundary",strField);
        if( szFieldStt != NULL )
        {
            contentType.strBoundary = strField;
        }


        szFieldStt = findOtherField((char*)strVal.c_str(),"charset",strField);
        if( szFieldStt != NULL )
        {
            contentType.strCharset = strField;
        }

        szFieldStt = findOtherField((char*)strVal.c_str(),"name",strField);
        if( szFieldStt != NULL )
        {
            contentType.strName = trim_left(trim_right(strField));
        }

        break;

    }

    return szStt;
}

/** @brief MData 헤더에서 Mime-Version를 가져온다.
 * @return Mime-Version를 가져온 이후 위치 포인트를 반환한다.
 */
char* CMMSPacketSend::setMimeVersion(char* szOrg)
{
    char* szStt=NULL;
    char* szEnd=NULL;
    char* szTag="MDATA";
    string strVal;
    string strField;

    szStt=szOrg;

    char* szFieldStt=NULL;
    //MIME-Version
    while(1)
    {
        szStt = matchString(szStt,szTag,strVal);
        if( szStt == NULL )
        {
            strErrorMsg = "MIME-Version 값을 찾을수 없습니다.";
            printf("MIME-Version 값을 찾을수 없습니다.\n");
            fflush(stdout);
            return NULL;
        }

        szFieldStt = findFirstField((char*)strVal.c_str(),"MIME-Version",strField);
        if( szFieldStt != NULL )
        {
            strMDataVersion = strField;
            break;
        }

    }

    return szStt;
}

char* CMMSPacketSend::getContentEncoding(char* szOrg, char* szTag, CMData &mData)
{
    char* szStt=NULL;
    char* szEnd=NULL;
    string strVal;
    string strField;

    szStt=szOrg;

    char* szFieldStt=NULL;

    while(1)
    {
        szStt = matchString(szStt,szTag,strVal);
        if( szStt == NULL )
        {
            return NULL;
        }

        szFieldStt = findFirstField((char*)strVal.c_str(),"Content-Transfer-Encoding",strField);
        if( szFieldStt != NULL )
        {
            mData.strEncoding = strField;
            break;
        }

    }

    return szStt;
}


char* CMMSPacketSend::getEachMData(char* szOrg, char* szTag, char* szBoundary, string &data)
{
    char* szStt=NULL;
    char* szEnd=NULL;
    string strVal;
    string strField;

    szStt=szOrg;

    char* szFieldStt=NULL;

    data = "";
    data.reserve(0);

    szEnd = strstr(szStt,szBoundary);
    if( szEnd == NULL )
        return NULL;

    if( (szEnd - szStt ) == 0 )
		return NULL;

    data.insert(0,szStt,szEnd-szStt);

    szEnd = szEnd + strlen(szBoundary) ;

    return strstr(szEnd,szTag);

}



char* CMMSPacketSend::getContentSvc(char* szOrg, char* szTag, CMData &mData)
{
    char* szStt=NULL;
    char* szEnd=NULL;
    string strVal;
    string strField;

    szStt=szOrg;

    char* szFieldStt=NULL;

    mData.strSvc = "";
    mData.strSvc.reserve(0);

    while(1)
    {

        szStt = matchString(szStt,szTag,strVal);
        if( szStt == NULL )
        {
            return NULL;
        }

        szFieldStt = findFirstField((char*)strVal.c_str(),"Content-Svc",strField);
        if( szFieldStt != NULL )
        {
            mData.strSvc = strField;
            break;
        }

    }

    return szStt;
}


/** @brief 한라인의 해당 태그의 두번째 값을 가져온다.
 *
 * filed="value" 형태를 체크 한다. 공백이나 다른 문자 들어 가면 안된다.
 * 엄격히 지켜야 한다.
 */
char* CMMSPacketSend::findOtherField(char* szOrg, char* szTag, string &strVal)
{
    char* szStt=NULL; /**< 문자열의 시작 포인트 */
    char* szEnd=NULL; /**< 문자열의 끝 포인트 */
    if ((szStt=strstr(szOrg,szTag))) {
        szStt=szStt+strlen(szTag)+1;
        if( szStt[0] != '\"' ) // 시작시 " 체크
            return NULL;

        szStt++;
        szEnd=strstr(szStt,"\""); // 다음 " 체크
        if( szEnd == NULL )
        {
 //           strErrorMsg = __FILE__ + ":" + __FUNCTION__ + ":" + __LINE__ +  "끝 큰따옴 가 없습니다.";
            return NULL;
        }
        strVal = "";
        strVal.reserve(0);
        strVal.insert(0,szStt,szEnd-szStt);
    }
    return szStt;
}


char* CMMSPacketSend::findFirstField(char* szOrg, char* szTag, string &strVal)
{
    char* szStt=NULL;
    char* szEnd=NULL;
    if ((szStt=strstr(szOrg,szTag)))
	{
        szStt=szStt+strlen(szTag)+1;
        szEnd=strstr(szStt,";"); // 줄의 마지막일경우 NULL을 아니면 ; 의 위치를 반환한다

        strVal = "";
        strVal.reserve(0);
        if( szEnd == NULL ) // 줄의 마지막 값일경우
            strVal = szStt;
        else  // 다음 값이 있을 경우
            strVal.insert(0,szStt,szEnd-szStt);
    }
    return szStt;
}

/** @brief MDATA 의 헤더를 가져온다.
 * @return MDATA 헤더 다음 MDATA 포인트를 반환한다.
 *
 * 2008-01-23 : MDataVersion / MDataType / MDataBoundary 를 가져온다.
 */
char* CMMSPacketSend::setMDataHeader(char* szOrg)
{
    char* szStt=NULL;
    char* szEnd=NULL;
    char* szTag="MDATA";
    string strVal;

    szStt=szOrg;

    string strField;
    char* szFieldStt=NULL;
    szStt=setMimeVersion(szStt);
    szStt=getContentType(szStt,contentType);
    if( 0 == contentType.strBoundary.length() )
        return NULL;
// DATA 다음 위치로 포인트 이동하기 : boundary 찾기
    szStt = strstr(szStt,contentType.strBoundary.c_str());
    if( szStt == NULL )
        return NULL;

    szStt = strstr(szStt,szTag); // 다음 MDATA 찾기
    if( szStt == NULL )
        return NULL;

    return szStt;
}

char* CMMSPacketSend::setMDataBody(char* szOrg, char* szTag)
{
    char* szStt=NULL;
    char* szEnd=NULL;
    char* szFieldStt=NULL;
    string strVal;
    string strField;
    CMData mData;

    szStt=szOrg;

    szStt=getContentType(szStt,mData.contentType);
    if( 0 == mData.contentType.strType.length() )
        return NULL;

    szStt=getContentEncoding(szStt,szTag,mData);
    if( szStt == NULL )
        return NULL;
    getContentSvc(szStt,szTag,mData);
    if( szStt == NULL )
        return NULL;

    printf("[%s] [%s] [%s] [%s] [%s]\n",
            mData.contentType.strType.c_str(),
            mData.contentType.strCharset.c_str(),
            mData.contentType.strName.c_str(),
            mData.strEncoding.c_str(),
            mData.strSvc.c_str()
          );

    fflush(stdout);

// DATA 다음 위치로 포인트 이동하기 : boundary 찾기
    szStt = strstr(szStt,contentType.strBoundary.c_str());
    if( szStt == NULL )
        return NULL;

    szStt = strstr(szStt,szTag); // 다음 MDATA 찾기
    if( szStt == NULL )
        return NULL;

    return szStt;
}

/*
void CMMSPacketSend::parseMData(char* szOrg)
{
    char* szTag="MDATA";
    char* szStt;
    char* szEnd;
    listMdataPosition pos,posPrev;
    string strVal;
    CMData mData;

    pos = listMData.begin();

    while( pos != listMData.end() )
    {
        posPrev = pos++;
        // 해당 string 메모리 제거 해야되나? 아니면 자동 제거 되나?
        listMData.erase(posPrev);
    }

    strMDataVersion="";
    strMDataVersion.reserve(0);

    szStt=setMDataHeader(szOrg);
    if( szStt == NULL )
    {
        printf("MData Header 정보 오류 ..\n");
        fflush(stdout);
        return;
    }

    char *szFieldStt=NULL;
    string strMDataBodyHeader;
    string strMDataBodyData;
    int ret;

    while( szStt )
    {
        szStt = getEachMData(szStt,szTag,(char*)contentType.strBoundary.c_str(),strVal);
        if( szStt == NULL )
            break;

        szFieldStt = getEachMDataBodyHeader((char*)strVal.c_str(),szTag,strMDataBodyHeader);
        if( szFieldStt == NULL )
            break;
        szFieldStt = getEachMDataBodyData(szFieldStt,szTag,strMDataBodyData);
        if( szFieldStt == NULL )
            break;

       ret =  getCMData(mData,(char*) strMDataBodyHeader.c_str(), (char*)strMDataBodyData.c_str());
       if( ret != 0 )
           break;
//       mData.ctnseq = ctnseq;

       listMData.push_back(mData);

//        printf("[%s] [%s] [%s] : [%s][%s]\n[%s]\n",
//                mData.contentType.strType.c_str(),
//                mData.contentType.strCharset.c_str(),
//                mData.contentType.strName.c_str(),
//               mData.strEncoding.c_str(),
//                mData.strSvc.c_str() ,
//                mData.strData.c_str()
//              );
//        fflush(stdout);


    }

    return;

}
*/
int CMMSPacketSend::parseMData(char* szOrg)
{
		int ret = 100;
    char* szTag="MDATA";
    char* szStt;
    char* szEnd;
    listMdataPosition pos,posPrev;
    string strVal;
    CMData mData;

    pos = listMData.begin();

    while( pos != listMData.end() )
    {
        posPrev = pos++;
        // 해당 string 메모리 제거 해야되나? 아니면 자동 제거 되나?
        listMData.erase(posPrev);
    }

    strMDataVersion="";
    strMDataVersion.reserve(0);

    szStt=setMDataHeader(szOrg);
    if( szStt == NULL )
    {
        printf("MData Header 정보 오류 ..\n");
        fflush(stdout);
        ret = -1;
        return ret;
    }

    char *szFieldStt=NULL;
    string strMDataBodyHeader;
    string strMDataBodyData;

    while( szStt )
    {
        szStt = getEachMData(szStt,szTag,(char*)contentType.strBoundary.c_str(),strVal);
        if( szStt == NULL )
            break;

        szFieldStt = getEachMDataBodyHeader((char*)strVal.c_str(),szTag,strMDataBodyHeader);
        if( szFieldStt == NULL )
            break;

        szFieldStt = getEachMDataBodyData(szFieldStt,szTag,strMDataBodyData);
        if( szFieldStt == NULL )
            break;

			// 20140218 getCMData return 0 fix
       ret =  getCMData(mData,(char*) strMDataBodyHeader.c_str(), (char*)strMDataBodyData.c_str());
       if( ret != 100 )
           break;
//       mData.ctnseq = ctnseq;

       listMData.push_back(mData);

   /*     printf("[%s] [%s] [%s] : [%s][%s]\n[%s]\n",
                mData.contentType.strType.c_str(),
                mData.contentType.strCharset.c_str(),
                mData.contentType.strName.c_str(),
                mData.strEncoding.c_str(),
                mData.strSvc.c_str() ,
                mData.strData.c_str()
              );
        fflush(stdout);
        */

    }

    return ret;

}

int CMMSPacketSend::getCMData(CMData& mData,char* header, char* body)
{
    char* szStt=NULL;
    char* szTag="MDATA";
    string strVal;
    char szCtnType[16];

    szStt=getContentType(header,mData.contentType);
//    if( 0 == mData.contentType.strBoundary.length() )
//      return -1;


    sprintf(szCtnType,mData.contentType.strType.c_str());
    if( strstr(szCtnType,"TXT") == 0 )
    {
        /* TXT 가 아닌경우 */
        mData.ctnseq = ++ctnseq;
//        mData.ctnseq = 0;
    }
	else
	{
    	mData.ctnseq = 0;
	}

    getContentEncoding(header,szTag,mData);
    getContentSvc(header,szTag,mData);

    mData.strData = "";
    mData.strData.reserve(0);
    szStt = body;
    while(1)
    {
        szStt = matchString(szStt,szTag,strVal);
        mData.strData += strVal;
        if( szStt == NULL )
            break;
    }
    return 100;
}


char* CMMSPacketSend::getEachMDataBodyData(char* szOrg, char* szTag, string &strMDataBodyData)
{
    char* szEnd = NULL;

    strMDataBodyData = "";
    strMDataBodyData.reserve(0);

    szEnd = strstr(szOrg,"\r\nMDATA:\r\n");
    if( szEnd == NULL )
        return NULL;

    if( (szEnd-szOrg) <= 0)
        return NULL;

    strMDataBodyData.insert(0,szOrg,szEnd-szOrg);

    return szEnd;
}


char* CMMSPacketSend::getEachMDataBodyHeader(char* szOrg, char* szTag, string &strMDataBodyHeader)
{
    char* szEnd = NULL;

    strMDataBodyHeader = "";
    strMDataBodyHeader.reserve(0);

    szEnd = strstr(szOrg,"\r\nMDATA:\r\n");
    if( szEnd == NULL )
        return NULL;

    if( (szEnd-szOrg) <= 0)
        return NULL;

    strMDataBodyHeader.insert(0,szOrg,szEnd-szOrg);
    strMDataBodyHeader += "\r\n";


    szEnd += strlen(szTag);


    szEnd = strstr(szEnd,szTag);


    return szEnd;
}

void CMMSPacketSend::parseBcastData(char* szOrg)
{
    char* szTag="BCASTDATA";
    char* szStt;
    char* szEnd;
    listCBcastDataPosition pos,posPrev;
    string strVal;

    pos = listCBcastData.begin();

    while( pos != listCBcastData.end() )
    {
        posPrev = pos++;
        // 해당 string 메모리 제거 해야되나?
        listCBcastData.erase(posPrev);
    }

    szStt=szOrg;

    while ((szStt=strstr(szStt,szTag)))
	{
        szStt=szStt+strlen(szTag)+1;
        szEnd=strstr(szStt,"\r\n");
        strVal = "";
        strVal.reserve(0);
        strVal.insert(0,szStt,szEnd-szStt);
//        listBcastData.push_back(strVal);
        //set bcastData
        pushBcastData(strVal);		// return void

        szStt=szEnd+2;
    }


    return ;
}

int CMMSPacketSend::parse(char* data)
{
	int ret = 100;
    ctnseq 		= 0;
    nCtnTypeCnt = 0;
    nCtnType 	= 0;

    nTextCnt 	= 0 ;
    nImgCnt  	= 0 ;
    nAugCnt  	= 0 ;
    nMpCnt   	= 0 ;
    strErrorMsg = "";
    strErrorMsg.reserve(0);

    //matchString(data,"ID",strId);
    matchString(data	,"KEY"			,strKey);
    matchString(data	, "SENDERKEY"	,strSenderKey);
    matchString(data	,"EXTEND"			,strExtend);
    //matchString(data	,"SUBJECT"		,strSubject);

    // charset : utf8(default), euc_kr
    matchString(data	,"ENCODING"		,strEncoding);
	// chat bubble type
    //matchString(data	,"MESSAGE_TYPE"    , strChatType);
    matchString(data	,"MSG_TYPE"    , strChatType);

	// M, N, I
	matchString(data	, "TARGETING"	, strTargeting);
    matchString(data	,"RECEIVER"		,strReceiver);
    //matchString(data	,"SENDER:"		,strSender);
    matchString(data	, "TMPLCD"		,strTmplCd);
	matchString(data	, "PUSHALARM"	, strPushAlarm);
    matchString(data	, "NATIONCODE", strNationCode);
    matchString(data	, "RESELLER_CODE", strResellerCode);
    matchString(data	, "UNSUBSCRIBE_PHONE", strUnsubPhone);
    matchString(data	, "UNSUBSCRIBE_AUTH", strUnsubAuth);

	matchString(data	, "APPUSERID"	, strAppUserId);
    matchString(data	, "ENCRYPT", strEncrypt);
	matchString(data	, "TEST_FRIEND"	, strTestFriend);

    if (this->strVarType == "vari") {
        // variable type
        matchString(data	, "MESSAGE_VARIABLE",strMessageVariable);
        matchString(data	, "BUTTON_VARIABLE",strButtonVariable);
        matchString(data	, "COUPON_VARIABLE", strCouponVariable);
        matchString(data	, "IMAGE_VARIABLE", strImageVariable);
        matchString(data	, "VIDEO_VARIABLE", strVideoVariable);
        matchString(data	, "COMMERCE_VARIABLE", strCommerceVariable);
        matchString(data	, "CAROUSEL_VARIABLE", strCarouselVariable);
        // Clear transaction-only fields
        strAddContent = "";
        strAdult = "";
        strHeader = "";
        strAttachment = "";
    }
    else {
        // transaction type
        matchString(data	, "ADDCONTENT", strAddContent);
        matchString(data	, "ADULT", strAdult);
        matchString(data	, "HEADER", strHeader);
        matchString(data	, "MESSAGE", strMessageVariable);
        matchString(data	, "ATTACHMENT", strAttachment);
        matchString(data	, "CAROUSEL", strCarouselVariable);
        // Clear variable-only fields  
        strButtonVariable = "";
        strCouponVariable = "";
        strImageVariable = "";
        strVideoVariable = "";
        strCommerceVariable = "";
    }

    #if (DEBUG >= 5)
	display();
    #endif

    return ret;								// return 100 == OK!!
}

int CMMSPacketSend::parse_free(char* data)
{
	int ret = 100;
    ctnseq 		= 0;
    nCtnTypeCnt = 0;
    nCtnType 	= 0;

    nTextCnt 	= 0 ;
    nImgCnt  	= 0 ;
    nAugCnt  	= 0 ;
    nMpCnt   	= 0 ;
    strErrorMsg = "";
    strErrorMsg.reserve(0);

    matchString(data	,"KEY"			,strKey);
    matchString(data	, "SENDERKEY"	,strSenderKey);
    // charset : utf8(default), euc_kr
    matchString(data	,"ENCODING"		,strEncoding);
	// chat bubble type
    matchString(data	,"MSGTYPE"    , strChatType);

	// M, N, I
	matchString(data	, "TARGETING"	, strTargeting);
    matchString(data	,"RECEIVER"		,strReceiver);

    matchString(data	, "TMPLCD"		,strTmplCd);
	matchString(data	, "PUSHALARM"	, strPushAlarm);
    matchString(data	, "NATIONCODE", strNationCode);
    matchString(data	, "EXTEND",     strExtend);
	matchString(data	, "APPUSERID"	, strAppUserId);
    matchString(data	, "ENCRYPT", strEncrypt);
	matchString(data	, "TEST_FRIEND"	, strTestFriend);
    matchString(data	, "RESELLER_CODE", strResellerCode);
    matchString(data	, "UNSUBSCRIBE_PHONE", strUnsubPhone);
    matchString(data	, "UNSUBSCRIBE_AUTH", strUnsubAuth);
    matchString(data	, "ADDCONTENT", strAddContent);
    matchString(data	, "ADULT", strAdult);
    matchString(data	, "HEADER", strHeader);
    matchString(data	, "MESSAGE", strMessageVariable);
    matchString(data	, "ATTACHMENT", strAttachment);
    matchString(data	, "CAROUSEL", strCarouselVariable);

    #if (DEBUG >= 5)
	display();
    #endif

    return ret;								// return 100 == OK!!
}


string CMMSPacketSend::decAesBase(Encrypt &en, const char * encStr, int encSize)
{
	int size;
	unsigned char *decBaseStr = (unsigned char*)__base64_decode(
		(const unsigned char *)encStr, encSize, &size);

	if (decBaseStr == NULL) {
		return "";
	}

	en.decrypt(decBaseStr, decBaseStr, size);

	if (size > 0 && decBaseStr[size - 1] != '\0') {
		decBaseStr[size] = '\0';
	}

	string decRet;
	//decRet = (char *)decBaseStr;
	decRet.assign((char*)decBaseStr, size);

	free(decBaseStr);

	return decRet;
}

void CMMSPacketSend::display()
{
    printf("\n");
    printf("key:[%s]"				,this->strKey.c_str());
    //printf("extend:[%s]"		,this->strExtend.c_str());
    //printf("subject:[%s]"		,this->strSubject.c_str());
    printf("receiver:[%s]"	,this->strReceiver.c_str());
    //printf("sender:[%s]"		,this->strSender.c_str());

    printf("SENDERKEY:[%s]"	,this->strSenderKey.c_str());
    printf("TMPLCD:[%s]"	,this->strTmplCd.c_str());

    printf("\n");

    //listCBcastDataPosition pos,posPrev;
    //pos = listCBcastData.begin();

    // while( pos != listCBcastData.end() )
    // {
    //     posPrev = pos++;
    //     printf("bcastData:key[%s][%s][%s]\n"
    //             ,(*posPrev).strKey.c_str()
    //             ,(*posPrev).strExtend.c_str()
    //             ,(*posPrev).strReceiver.c_str()
    //             );
    // }

    // printf("MDATA Version [%s]\n"		,this->strMDataVersion.c_str());
    // printf("MDATA Type [%s]\n"			,this->contentType.strType.c_str());
    // printf("MDATA Boundary [%s]\n"	,this->contentType.strBoundary.c_str());
    //
    //
    // listMdataPosition pos2,pos2Prev;
    // pos2 = listMData.begin();
    //
    // while( pos2 != listMData.end() )
    // {
    //     pos2Prev = pos2++;
    //
    //     printf("MData:[%s][%s][%s] [%s][%s]\n[%s]\n",
    //             (*pos2Prev).contentType.strType.c_str(),
    //             (*pos2Prev).contentType.strCharset.c_str(),
    //             (*pos2Prev).contentType.strName.c_str(),
    //             (*pos2Prev).strEncoding.c_str(),
    //             (*pos2Prev).strSvc.c_str(),
    //             (*pos2Prev).strData.c_str()
    //           );
    //
    // }

    fflush(stdout);

    return;
}

char* CMMSPacketSend::getIdValue()
{
    return (char*)strId.c_str();
}

char* CMMSPacketSend::getKeyValue()
{
    return (char*)strKey.c_str();
}

char* CMMSPacketSend::getEncodingValue()
{
	return (char*)strEncoding.c_str();
}

char* CMMSPacketSend::getChatTypeValue() {
	return (char*)strChatType.c_str();
}

char* CMMSPacketSend::getTargetingValue() {
	return (char*)strTargeting.c_str();
}

char* CMMSPacketSend::getSubjectValue()
{
    return (char*)strSubject.c_str();
}

char* CMMSPacketSend::getReceiverValue()
{
    return (char*)strReceiver.c_str();
}

char* CMMSPacketSend::getSenderValue()
{
    return (char*)strSender.c_str();
}

char* CMMSPacketSend::getContentCntValue()
{
    return (char*)strContentCnt.c_str();
}

char* CMMSPacketSend::getTextCntValue()
{
    return (char*)strTextCnt.c_str();
}

char* CMMSPacketSend::getImgCntValue()
{
    return (char*)strImgCnt.c_str();
}

char* CMMSPacketSend::getSenderKeyValue()
{
	return (char*)strSenderKey.c_str();
}

char* CMMSPacketSend::getTmplCdValue()
{
	return (char*)strTmplCd.c_str();
}

char* CMMSPacketSend::getButtonValue()
{
    return (char*)strButton.c_str();
}

char* CMMSPacketSend::getMsgOrgSizeValue()
{
    return (char*)strMsgOrgSize.c_str();
}

char* CMMSPacketSend::getPhoneSizeValue()
{
    return (char*)strPhoneSize.c_str();
}

char* CMMSPacketSend::getMsgBodyValue()
{
    return (char*)strMsgBody.c_str();
}

char* CMMSPacketSend::getImgPathValue()
{
    return (char*)strImgPath.c_str();
}

char* CMMSPacketSend::getImgLinkValue()
{
    return (char*)strImgLink.c_str();
}

char* CMMSPacketSend::getUserKeyValue()
{
    return (char*)strUserKey.c_str();
}

char* CMMSPacketSend::getMsgGrpCdValue()
{
    return (char*)strMsgGrpCd.c_str();
}

char* CMMSPacketSend::getAdFlagValue()
{
    return (char*)strAdFlag.c_str();
}

char* CMMSPacketSend::getWideValue()
{
    return (char*)strWide.c_str();
}

char* CMMSPacketSend::getKkoImgUrlValue()
{
  return (char*)strKkoImgUrl.c_str();
}

char* CMMSPacketSend::getEncryptValue()
{
  return (char*)strEncrypt.c_str();
}

char* CMMSPacketSend::getPushAlarmValue()
{
	return (char *)strPushAlarm.c_str();
}

char* CMMSPacketSend::getAppUserIdValue()
{
	return (char *)strAppUserId.c_str();
}

char* CMMSPacketSend::getTestFriendValue()
{
	return (char *)strTestFriend.c_str();
}

char* CMMSPacketSend::getMessageVariableValue()
{
	return (char *)strMessageVariable.c_str();
}

char* CMMSPacketSend::getButtonVariableValue()
{
	return (char *)strButtonVariable.c_str();
}

char* CMMSPacketSend::getCouponVariableValue()
{
	return (char *)strCouponVariable.c_str();
}

char* CMMSPacketSend::getImageVariableValue()
{
	return (char *)strImageVariable.c_str();
}

char* CMMSPacketSend::getVideoVariableValue()
{
	return (char *)strVideoVariable.c_str();
}

char* CMMSPacketSend::getCommerceVariableValue()
{
	return (char *)strCommerceVariable.c_str();
}

char* CMMSPacketSend::getCarouselVariableValue()
{
	return (char *)strCarouselVariable.c_str();
}

char* CMMSPacketSend::getRepFlagValue()
{
	return (char *)strRepFlag.c_str();
}

char* CMMSPacketSend::getRepMsgTitleValue()
{
	return (char *)strRepMsgTitle.c_str();
}

char* CMMSPacketSend::getRepMsgBodyValue()
{
	return (char *)strRepMsgBody.c_str();
}

char * CMMSPacketSend::getReserveValue() {
    return (char *)strReserve.c_str();
}
char* CMMSPacketSend::getNationCodeValue()
{
	return (char *)strNationCode.c_str();
}

char* CMMSPacketSend::getResellerCodeValue()
{
	return (char *)strResellerCode.c_str();
}

char* CMMSPacketSend::getUnsubPhoneValue()
{
	return (char *)strUnsubPhone.c_str();
}

char* CMMSPacketSend::getUnsubAuthValue()
{
	return (char *)strUnsubAuth.c_str();
}
char* CMMSPacketSend::getAdultValue()
{
	return (char *)strAdult.c_str();
}

char* CMMSPacketSend::getHeaderValue()
{
	return (char *)strHeader.c_str();
}

char* CMMSPacketSend::getAttachmentValue()
{
	return (char *)strAttachment.c_str();
}

char* CMMSPacketSend::getAddContentValue()
{
    return (char *)strAddContent.c_str();
}



/** @brief 해당 태그부터 \r\n 까지 문자열을 가져온다.
 * @return 가져온 문자열의 다음 위치 포인트를 반환한다.
 */
char* CMMSPacketSend::matchString(char* szOrg, const char *szTag, string& strVal)
{
    char* szStt;
    char* szEnd=NULL;
    strVal = "";
    strVal.reserve(0);

    if( szOrg == NULL )
        return NULL;
    if ((szStt=strstr(szOrg,szTag))) {
        // if szTag's last char is not :, +1 is value
        if (szTag[strlen(szTag)-1] == ':')
            szStt=szStt+strlen(szTag);
        else
            szStt=szStt+strlen(szTag)+1;

        szEnd=strstr(szStt,"\r\n");
        if( (szEnd - szStt) == 0 )
        {
            strVal = "";
            strVal.reserve(0);
//            strVal = "\r\n";

        } else {
        //    return NULL;
            strVal = "";
            strVal.reserve(0);
            if( szEnd == NULL )
                strVal.insert(0,szStt);
            else
                strVal.insert(0,szStt,szEnd-szStt);
        }
//        memcpy(szVal,szStt,szEnd-szStt);
    }
    if( szStt == NULL ) return NULL;
    if( szEnd == NULL ) return NULL;
    return szEnd+2;
}

int CMMSPacketSend::getMDataNext(CMData& mData)
{
    m_MDataPos++;

    if( m_MDataPos != listMData.end() )
    {
        mData.contentType.strType = (*m_MDataPos).contentType.strType;
        mData.contentType.strCharset = (*m_MDataPos).contentType.strCharset;
        mData.contentType.strName = (*m_MDataPos).contentType.strName;
        mData.strEncoding = (*m_MDataPos).strEncoding;
        mData.strSvc = (*m_MDataPos).strSvc;
        mData.strData = (*m_MDataPos).strData;
        mData.ctnseq = (*m_MDataPos).ctnseq;
        return 0;
    }

    return -1;

}

int CMMSPacketSend::getMDataFirst(CMData& mData)
{
    m_MDataPos = listMData.begin();

    if( m_MDataPos != listMData.end() )
    {
        mData.contentType.strType = (*m_MDataPos).contentType.strType;
        mData.contentType.strCharset = (*m_MDataPos).contentType.strCharset;
        mData.contentType.strName = (*m_MDataPos).contentType.strName;
        mData.strEncoding = (*m_MDataPos).strEncoding;
        mData.strSvc = (*m_MDataPos).strSvc;
        mData.strData = (*m_MDataPos).strData;
        mData.ctnseq = (*m_MDataPos).ctnseq;
        return 0;
    }

    return -1;
}


int CMMSPacketSend::getBcastFirst(CBcastData& bCastData)
{
    m_BcastData = listCBcastData.begin();

    if( m_BcastData != listCBcastData.end() )
    {
        bCastData.strSeq = (*m_BcastData).strSeq;
        bCastData.strKey = (*m_BcastData).strKey;
        bCastData.strExtend = (*m_BcastData).strExtend;
        bCastData.strReceiver = (*m_BcastData).strReceiver;

        return 0;
    }

    return -1;
}

int CMMSPacketSend::getBcastNext(CBcastData& bCastData)
{
    m_BcastData++;

    if( m_BcastData != listCBcastData.end() )
    {
        bCastData.strSeq = (*m_BcastData).strSeq;
        bCastData.strKey = (*m_BcastData).strKey;
        bCastData.strExtend = (*m_BcastData).strExtend;
        bCastData.strReceiver = (*m_BcastData).strReceiver;

        return 0;
    }

    return -1;
}

int CMMSPacketSend::getCtnSeq()
{
    return ctnseq;
}

void CMMSPacketSend::pushBcastData(string data)
{
    CBcastData bCastData;
    string tokenString = data;
    std::vector <std::string> entryVector;
    string separators = "|";
    int nCnt;

    split(tokenString,separators,entryVector);

    nCnt = entryVector.size();
    if( 4 < nCnt )
        nCnt = 4;

    switch(nCnt) {
        case 4:
            bCastData.strReceiver = entryVector.at(3);
        case 3:
            bCastData.strExtend = entryVector.at(2);
        case 2:
            bCastData.strKey = entryVector.at(1);
        case 1:
            bCastData.strSeq = entryVector.at(0);
            listCBcastData.push_back(bCastData);
    }


    return ;
}



int CMMSPacketSend::getCtnType()
{
    return nCtnType;
}

void CMMSPacketSend::setCtnType()
{
//    printf("---------- ctnTypeCnt [%d]\n",nCtnTypeCnt);
    fflush(stdout);
    switch(nCtnTypeCnt) {
        case 1:
            nCtnType  = 0;
            break;
         case 11:
            nCtnType  = 1;
            break;
         case 21:
            nCtnType  = 2;
            break;
         case 31:
         case 10:
            nCtnType  = 3;
            break;
         case 20:
            nCtnType  = 4;
            break;
         case 101:
            nCtnType  = 5;
            break;
         case 111:
            nCtnType  = 6;
            break;
         case 121:
            nCtnType  = 7;
            break;
          case 120:
            nCtnType  = 8;
            break;
          case 110:
            nCtnType  = 9;
            break;
        default:
            nCtnType = 0;
            break;
    }

}


void CMMSPacketSend::split(std::string& text, std::string& separators, std::vector<std::string>& words)
{
    int n = text.length();
    int start=0, stop;

    stop = text.find_first_of(separators) + 1;
    words.push_back(text.substr(start, stop - start-1));
    start = stop;

    while ((start > 0) && (start <= n)) {
        stop = text.find_first_of(separators,start) +1;
        if ((stop <=0) || (stop > n))
		{
            words.push_back(text.substr(start, stop - start));
            stop = n;
            break;
        }

        words.push_back(text.substr(start, stop - start-1));
        start = stop;
    }
}

char* CMMSPacketSend::getErrorMsg()
{
    return (char*)strErrorMsg.c_str();
}



